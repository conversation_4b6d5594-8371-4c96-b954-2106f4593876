# Chinook Testing Suite Documentation

This directory contains comprehensive testing documentation for the Chinook database implementation using Pest PHP framework with Laravel 12 modern patterns.

## Documentation Structure

### Core Testing Documentation

1. **[Test Architecture Overview](010-test-architecture-overview.md)** - Comprehensive test architecture with Mermaid diagrams
2. **[Unit Testing Guide](020-unit-testing-guide.md)** - Model, trait, service, and enum testing strategies
3. **[Feature Testing Guide](030-feature-testing-guide.md)** - API, web routes, and Filament admin panel testing
4. **[Integration Testing Guide](040-integration-testing-guide.md)** - Database relationships and end-to-end workflows
5. **[Test Data Management](050-test-data-management.md)** - Factories, seeders, and database state management

### Specialized Testing Areas

1. **[RBAC Testing Guide](060-rbac-testing-guide.md)** - spatie/laravel-permission testing with hierarchical roles
2. **[Trait Testing Guide](070-trait-testing-guide.md)** - HasSecondaryUnique<PERSON>ey, HasSlug, Categorizable testing
3. **[Hierarchical Data Testing](080-hierarchical-data-testing.md)** - Closure table + adjacency list hybrid testing
4. **[Performance Testing Guide](090-performance-testing-guide.md)** - SQLite WAL optimization and load testing

## Testing Philosophy

The Chinook testing suite follows modern Laravel 12 and Pest PHP patterns with emphasis on:

- **Comprehensive Coverage**: Unit, feature, and integration tests
- **Performance Focus**: SQLite WAL journal mode optimizations
- **RBAC Integration**: Complete permission and role testing
- **Trait Testing**: Specialized testing for all custom traits
- **Hierarchical Data**: Advanced testing for closure table architecture
- **Accessibility**: WCAG 2.1 AA compliant documentation

## Quick Start

### Prerequisites

```bash
# Install Pest and required plugins
composer require --dev pestphp/pest
composer require --dev pestphp/pest-plugin-laravel
composer require --dev pestphp/pest-plugin-livewire
composer require --dev pestphp/pest-plugin-faker
composer require --dev pestphp/pest-plugin-type-coverage

# Initialize Pest
./vendor/bin/pest --init
```

### Test Structure

```text
tests/
├── Feature/
│   ├── Api/
│   ├── Web/
│   ├── Filament/
│   └── Livewire/
├── Unit/
│   ├── Models/
│   ├── Traits/
│   ├── Services/
│   └── Enums/
├── Integration/
│   ├── Database/
│   ├── Hierarchical/
│   └── Performance/
└── Pest.php
```

## Testing Standards

### Laravel 12 Modern Patterns

- Use `cast()` method over `$casts` property
- Modern factory definitions with state methods
- Current Laravel 12 syntax for all framework features

### Pest PHP Framework

- Describe/it blocks for test organization
- Expectation-based assertions
- Helper functions for common operations
- Parallel testing for performance

### WCAG 2.1 AA Compliance

- High-contrast color palette for diagrams
- Screen reader compatible documentation
- Accessible navigation structure
- Comprehensive alt text for visual elements

## Related Documentation

- [Chinook Models Guide](../010-chinook-models-guide.md) - Model implementations
- [Chinook Factories Guide](../030-chinook-factories-guide.md) - Factory patterns
- [Pest Testing Guide](../packages/testing/010-pest-testing-guide.md) - General Pest patterns
- [Filament Testing](../filament/testing/README.md) - Admin panel testing

---

**Navigation:**

- **Previous:** [Chinook Advanced Features Guide](../050-chinook-advanced-features-guide.md)
- **Next:** [Test Architecture Overview](010-test-architecture-overview.md)
- **Up:** [Chinook Documentation Index](../000-chinook-index.md)
